"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _Collection = _interopRequireDefault(require("../Collection"));
var _data = require("../../../data/data");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class CardCollection extends _Collection.default {
  constructor(user, models) {
    super(user, models, 'cards', 'cardId');
    this.starterDeckId = 821;
  }

  // Owned cards * their quantities
  get deck() {
    let deck = [];
    this.keys.forEach(card => {
      const quantity = this.getQuantity(card);
      deck.push(...Array(quantity).fill(card));
    });
    return deck;
  }
  get hasCards() {
    const hasStarterDeck = this.user.inventory.includes(this.starterDeckId);
    const hasCards = this.keys.length > 0;
    return hasStarterDeck && hasCards;
  }
  getQuantity(card) {
    return this.collection[card].quantity;
  }
  add(card, quantity = 1) {
    if (this.includes(card)) {
      this.collection[card].update({
        quantity: this.getQuantity(card) + quantity
      });
    } else {
      super.add({
        userId: this.user.id,
        cardId: card,
        quantity: quantity,
        memberQuantity: 0
      });
    }
  }
  toJSON() {
    return this.keys.map(cardId => this.cardToJSON(cardId));
  }
  cardToJSON(cardId) {
    const card = _data.cards[cardId];
    return {
      id: parseInt(cardId),
      powerId: card.powerId,
      element: card.element,
      color: card.color,
      value: card.value,
      quantity: this.getQuantity(cardId)
    };
  }
}
exports.default = CardCollection;