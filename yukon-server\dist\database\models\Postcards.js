"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseModel = _interopRequireDefault(require("../BaseModel"));
var _pick = _interopRequireDefault(require("../../utils/pick"));
var _sequelize = _interopRequireDefault(require("sequelize"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
const systemName = 'sys';
class Postcards extends _BaseModel.default {
  static init(sequelize, DataTypes) {
    return super.init({
      id: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      senderId: {
        type: DataTypes.INTEGER(11),
        allowNull: true
      },
      postcardId: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      sendDate: {
        type: _sequelize.default.DATE(3),
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      details: {
        type: DataTypes.STRING(255),
        allowNull: true,
        defaultValue: null
      },
      hasRead: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: 0
      },
      senderName: {
        type: DataTypes.VIRTUAL,
        get() {
          return this.user?.username;
        }
      }
    }, {
      sequelize,
      timestamps: false,
      tableName: 'postcards'
    });
  }
  static associate({
    users
  }) {
    this.belongsTo(users, {
      foreignKey: 'userId'
    });
    this.hasOne(users, {
      foreignKey: 'id',
      sourceKey: 'senderId',
      as: 'user'
    });
  }
  toJSON() {
    const postcard = (0, _pick.default)(this, 'id', 'senderId', 'postcardId', 'sendDate', 'details', 'hasRead');
    postcard.senderName = this.senderId !== null ? this.senderName : systemName;
    return postcard;
  }
}
exports.default = Postcards;