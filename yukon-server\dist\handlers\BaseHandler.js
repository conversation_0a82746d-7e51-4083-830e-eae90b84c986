"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _PluginManager = _interopRequireDefault(require("../plugin/PluginManager"));
var _events = _interopRequireDefault(require("events"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class BaseHandler {
  constructor(id, users, db, config) {
    this.id = id;
    this.users = users;
    this.db = db;
    this.config = config;
    this.logging = true;
    this.plugins;
    this.events = new _events.default({
      captureRejections: true
    });
    this.events.on('error', error => {
      this.error(error);
    });
  }
  startPlugins(pluginsDir = '') {
    this.plugins = new _PluginManager.default(this, pluginsDir);
  }
  handle(message, user) {
    try {
      if (this.logging) {
        console.log(`[${this.id}] Received: ${message.action} ${JSON.stringify(message.args)}`);
      }
      if (this.handleGuard(message, user)) {
        return user.close();
      }
      this.events.emit(message.action, message.args, user);
      if (user.events) {
        user.events.emit(message.action, message.args, user);
      }
    } catch (error) {
      this.error(error);
    }
  }
  handleGuard(message, user) {
    return false;
  }
  close(user) {
    delete this.users[user.socket.id];
  }
  error(error) {
    console.error(`[${this.id}] ERROR: ${error.stack}`);
  }
}
exports.default = BaseHandler;