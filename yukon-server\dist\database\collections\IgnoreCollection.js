"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _Collection = _interopRequireDefault(require("../Collection"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class IgnoreCollection extends _Collection.default {
  constructor(user, models) {
    super(user, models, 'ignores', 'ignoreId');
  }
  add(id) {
    super.add({
      userId: this.user.id,
      ignoreId: id
    });
  }
  toJSON() {
    let ignores = [];
    for (let ignore in this.collection) {
      let username = this.collection[ignore].user.username;
      ignores.push({
        id: parseInt(ignore),
        username: username
      });
    }
    return ignores;
  }
}
exports.default = IgnoreCollection;