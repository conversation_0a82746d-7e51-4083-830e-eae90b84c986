"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _CardMatchmaker = _interopRequireDefault(require("./CardMatchmaker"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class MatchmakerFactory {
  static types = {
    'card': _CardMatchmaker.default
  };
  static createMatchmaker(matchmaker, room) {
    return new this.types[matchmaker.game](matchmaker, room);
  }
}
exports.default = MatchmakerFactory;