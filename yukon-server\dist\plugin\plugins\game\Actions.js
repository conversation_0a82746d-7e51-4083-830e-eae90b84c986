"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Actions extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'send_position': this.sendPosition,
      'send_frame': this.sendFrame,
      'snowball': this.snowball
    };
  }
  sendPosition(args, user) {
    if (!(0, _validation.hasProps)(args, 'x', 'y')) {
      return;
    }
    if (!(0, _validation.isInRange)(args.x, 0, 1520)) {
      return;
    }
    if (!(0, _validation.isInRange)(args.y, 0, 960)) {
      return;
    }
    user.x = args.x;
    user.y = args.y;
    user.frame = 1;
    user.room.send(user, 'send_position', {
      id: user.id,
      x: args.x,
      y: args.y
    });
  }
  sendFrame(args, user) {
    if (!(0, _validation.hasProps)(args, 'frame')) {
      return;
    }
    if (!(0, _validation.isInRange)(args.frame, 1, 26)) {
      return;
    }
    if (args.set) {
      user.frame = args.frame;
    } else {
      user.frame = 1;
    }
    user.room.send(user, 'send_frame', {
      id: user.id,
      frame: args.frame,
      set: args.set
    });
  }
  snowball(args, user) {
    if (!(0, _validation.hasProps)(args, 'x', 'y')) {
      return;
    }
    if (!(0, _validation.isInRange)(args.x, 0, 1520)) {
      return;
    }
    if (!(0, _validation.isInRange)(args.y, 0, 960)) {
      return;
    }
    user.room.send(user, 'snowball', {
      id: user.id,
      x: args.x,
      y: args.y
    });
  }
}
exports.default = Actions;