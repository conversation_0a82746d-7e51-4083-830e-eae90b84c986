"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseHandler = _interopRequireDefault(require("./BaseHandler"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class LoginHandler extends _BaseHandler.default {
  constructor(id, users, db, config) {
    super(id, users, db, config);
    this.logging = false;
    this.startPlugins('/login');
  }
}
exports.default = LoginHandler;