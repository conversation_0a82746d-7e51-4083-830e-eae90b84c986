"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Puck extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'get_puck': this.getPuck,
      'move_puck': this.movePuck
    };
    this.rinkRoomId = 802;
    this.puckX = 0;
    this.puckY = 0;
  }
  getPuck(args, user) {
    if (user.room.id !== this.rinkRoomId) return;
    user.send('get_puck', {
      x: this.puckX,
      y: this.puckY
    });
  }
  movePuck(args, user) {
    if (user.room.id !== this.rinkRoomId) return;
    if (!(0, _validation.hasProps)(args, 'x', 'y', 'speedX', 'speedY')) return;
    if (!(0, _validation.isNumber)(args.x)) return;
    if (!(0, _validation.isNumber)(args.y)) return;
    if (!(0, _validation.isNumber)(args.speedX)) return;
    if (!(0, _validation.isNumber)(args.speedY)) return;
    this.puckX = args.x;
    this.puckY = args.y;
    user.room.send(user, 'move_puck', {
      x: args.x,
      y: args.y,
      speedX: args.speedX,
      speedY: args.speedY
    });
  }
}
exports.default = Puck;