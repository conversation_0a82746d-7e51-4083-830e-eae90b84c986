/*
MIT License

Copyright (c) 2022 wizguin <https://github.com/wizguin>

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
*/
"use strict";(self.webpackChunkyukon=self.webpackChunkyukon||[]).push([[20],{20020:(t,e,r)=>{r.r(e),r.d(e,{default:()=>p});r(52675),r(89463),r(2259),r(45700),r(23792),r(89572),r(94170),r(62010),r(2892),r(59904),r(84185),r(40875),r(10287),r(26099),r(60825),r(47764),r(62953);var n=r(86462),o=r(48752);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,c(n.key),n)}}function c(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}function a(t,e,r){return e=l(e),function(t,e){if(e&&("object"===i(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,f()?Reflect.construct(e,r||[],l(t).constructor):e.apply(t,r))}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(f=function(){return!!t})()}function l(t){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},l(t)}function s(t,e){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},s(t,e)}var p=function(t){function e(t,r,n){var i;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(i=a(this,e,[t,null!=r?r:0,null!=n?n:0])).name,i.goToButton;var u=t.add.image(0,0,"postcards/sprites/21","21");u.setOrigin(0,0),i.add(u);var c=t.add.text(44,558,"",{});c.text="penguin88888888",c.setStyle({color:"#000066",fixedWidth:424,fontFamily:"CCFaceFront",fontSize:"40px"}),c.setPadding({left:5,right:5}),i.add(c);var f=new o.A(t,847,616);return i.add(f),f.roomId=806,i.name=c,i.goToButton=f,i}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&s(t,e)}(e,t),r=e,n&&u(r.prototype,n),i&&u(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(n.A)},86462:(t,e,r)=>{r.d(e,{A:()=>l});r(52675),r(89463),r(2259),r(45700),r(23792),r(89572),r(23288),r(94170),r(62010),r(2892),r(59904),r(84185),r(40875),r(10287),r(26099),r(60825),r(47764),r(62953);function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,i(n.key),n)}}function i(t){var e=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!=n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==n(e)?e:e+""}function u(t,e,r){return e=a(e),function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,c()?Reflect.construct(e,r||[],a(t).constructor):e.apply(t,r))}function c(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(c=function(){return!!t})()}function a(t){return a=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},a(t)}function f(t,e){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},f(t,e)}var l=function(t){function e(t,r,n){var o;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(o=u(this,e,[t,r,n])).name=null,o.goToButton=null,o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f(t,e)}(e,t),r=e,(n=[{key:"setText",value:function(t){var e=t.senderName,r=t.details,n=t.sendDate;this.name&&(this.name.text=e),this.details&&(this.details.text=r),this.date&&(this.date.text=this.getDateText(n)),this.receiverName&&(this.receiverName.text=this.world.client.penguin.username)}},{key:"getDateText",value:function(t){return new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}},{key:"enableInput",value:function(){this.goToButton&&this.goToButton.enableInput()}}])&&o(r.prototype,n),i&&o(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(r(12445).A)},48752:(t,e,r)=>{r.d(e,{A:()=>p});r(52675),r(89463),r(2259),r(45700),r(23792),r(89572),r(94170),r(2892),r(59904),r(84185),r(40875),r(10287),r(26099),r(60825),r(47764),r(62953);var n=r(17483),o=r(66298);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,c(n.key),n)}}function c(t){var e=function(t,e){if("object"!=i(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==i(e)?e:e+""}function a(t,e,r){return e=l(e),function(t,e){if(e&&("object"===i(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(t,f()?Reflect.construct(e,r||[],l(t).constructor):e.apply(t,r))}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(t){}return(f=function(){return!!t})()}function l(t){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},l(t)}function s(t,e){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},s(t,e)}var p=function(t){function e(t,r,n,o,i){var u;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e),(u=a(this,e,[t,null!=r?r:760,null!=n?n:480,o||"mail",null!=i?i:"goto_button"])).roomId=0,u.setOrigin(.5031847133757962,.5),u}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&s(t,e)}(e,t),r=e,(n=[{key:"enableInput",value:function(){var t=this,e=new o.A(this);e.spriteName="goto_button",e.callback=function(){return t.onClick()},e.activeFrame=!1}},{key:"onClick",value:function(){if(this.roomId in this.crumbs.scenes.rooms){var t=this.crumbs.scenes.rooms[this.roomId];this.world.room.key!==t.key&&this.world.client.sendJoinRoom(this.roomId,t.key,t.x,t.y)}this.interface.main.mail.close()}}])&&u(r.prototype,n),i&&u(r,i),Object.defineProperty(r,"prototype",{writable:!1}),r;var r,n,i}(n.A)}}]);