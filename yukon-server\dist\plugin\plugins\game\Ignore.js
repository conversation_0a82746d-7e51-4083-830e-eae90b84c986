"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Ignore extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'ignore_add': this.ignoreAdd,
      'ignore_remove': this.ignoreRemove
    };
  }
  async ignoreAdd(args, user) {
    if (!(0, _validation.hasProps)(args, 'id')) {
      return;
    }
    if (!(0, _validation.isNumber)(args.id)) {
      return;
    }
    if (user.id == args.id) {
      return;
    }
    if (user.buddies.includes(args.id)) {
      return;
    }
    if (user.ignores.includes(args.id)) {
      return;
    }
    let ignore = this.usersById[args.id];
    let username;
    if (ignore) {
      username = ignore.username;
      ignore.clearBuddyRequest(user.id);
    } else {
      username = await this.db.getUsername(args.id);
    }
    if (!username) {
      return;
    }
    user.clearBuddyRequest(args.id);
    user.ignores.add(args.id);
    user.send('ignore_add', {
      id: args.id,
      username: username
    });
  }
  ignoreRemove(args, user) {
    if (!user.ignores.includes(args.id)) {
      return;
    }
    user.ignores.remove(args.id);
    user.send('ignore_remove', {
      id: args.id
    });
  }
}
exports.default = Ignore;