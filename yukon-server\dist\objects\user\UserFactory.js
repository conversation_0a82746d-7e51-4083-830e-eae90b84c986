"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _default;
var _GameUserMixin = _interopRequireDefault(require("./GameUserMixin"));
var _UserMixin = _interopRequireDefault(require("./UserMixin"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function _default(server, socket) {
  let user = server.db.buildUser();
  let mixin;
  switch (server.id) {
    case 'Login':
      mixin = _UserMixin.default;
      break;
    default:
      mixin = _GameUserMixin.default;
      break;
  }
  Object.assign(user, mixin);
  user.init(server, socket);
  return user;
}