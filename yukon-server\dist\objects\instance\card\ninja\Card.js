"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _data = require("../../../../../data/data");
var _pick = _interopRequireDefault(require("../../../../utils/pick"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Card {
  constructor(id) {
    this.id = parseInt(id);
    const card = _data.cards[id];
    this.powerId = card.powerId;
    this.element = card.element;
    this.color = card.color;
    this.value = card.value;
    this.originalElement = card.element;
  }
  toJSON() {
    return (0, _pick.default)(this, 'id', 'powerId', 'element', 'color', 'value');
  }
}
exports.default = Card;