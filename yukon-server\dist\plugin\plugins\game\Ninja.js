"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Sensei extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'get_ninja': this.getNinja
    };
  }
  getNinja(args, user) {
    user.send('get_ninja', {
      rank: user.ninjaRank,
      progress: user.ninjaProgress,
      cards: user.cards
    });
  }
}
exports.default = Sensei;