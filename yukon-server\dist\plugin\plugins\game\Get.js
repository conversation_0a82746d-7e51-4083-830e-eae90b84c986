"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Get extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'get_player': this.getPlayer
    };
  }
  async getPlayer(args, user) {
    if (!(0, _validation.hasProps)(args, 'id')) {
      return;
    }
    if (!(0, _validation.isNumber)(args.id)) {
      return;
    }
    if (args.id in this.usersById) {
      return user.send('get_player', {
        penguin: this.usersById[args.id].anonymous
      });
    }
    if (!user.buddies.includes(args.id)) {
      return;
    }
    let u = await this.db.getUserById(args.id);
    if (u) {
      user.send('get_player', {
        penguin: u.anonymous
      });
    }
  }
}
exports.default = Get;