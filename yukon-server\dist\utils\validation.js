"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.hasProps = hasProps;
exports.isInRange = isInRange;
exports.isLength = isLength;
exports.isNumber = isNumber;
exports.isString = isString;
function hasProps(object, ...props) {
  for (let prop of props) {
    if (!(prop in object)) {
      return false;
    }
  }
  return true;
}
function isNumber(value) {
  return typeof value == 'number' && !isNaN(value) && Number.isInteger(value);
}
function isString(value) {
  return typeof value == 'string';
}
function isInRange(value, min, max) {
  if (!isNumber(value) || value < min || value > max) {
    return false;
  }
  return true;
}
function isLength(value, min, max) {
  if (!isString(value) || value.length < min || value.length > max) {
    return false;
  }
  return true;
}