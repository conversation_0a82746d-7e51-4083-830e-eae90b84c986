"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _CardInstance = _interopRequireDefault(require("./card/CardInstance"));
var _SledInstance = _interopRequireDefault(require("./sled/SledInstance"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class InstanceFactory {
  static types = {
    'card': _CardInstance.default,
    'sled': _SledInstance.default
  };
  static createInstance(waddle) {
    return new this.types[waddle.game](waddle);
  }
}
exports.default = InstanceFactory;