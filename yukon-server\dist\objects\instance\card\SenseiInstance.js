"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _CardInstance = _interopRequireDefault(require("./CardInstance"));
var _SenseiNinja = _interopRequireDefault(require("./ninja/SenseiNinja"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class SenseiInstance extends _CardInstance.default {
  constructor(user) {
    super({
      users: [user]
    });
    this.user = user;
    this.senseiData = {
      username: 'Sensei',
      color: 14,
      ninjaRank: 10,
      sensei: true
    };
    this.sensei;
    this.me;
  }
  init() {
    super.init();
    this.sensei = new _SenseiNinja.default();
    this.me = this.ninjas[this.user.id];
    this.sensei.opponent = this.me;
    this.me.opponent = this.sensei;
  }
  start() {
    let users = [this.senseiData, {
      username: this.user.username,
      color: this.user.color,
      ninjaRank: this.user.ninjaRank
    }];
    this.send('start_game', {
      users: users
    });
    this.started = true;
  }
  handleStartGame() {
    this.start();
  }
  handleSendDeal(args, user) {
    if (this.me.hasDealt) return;
    let canBeatSensei = user.ninjaRank >= this.itemAwards.length - 1;
    let cards = this.me.dealCards(canBeatSensei);
    let senseiCards = this.sensei.dealCards(cards, canBeatSensei);
    user.send('send_deal', {
      cards: cards
    });
    user.send('send_opponent_deal', {
      deal: senseiCards.length
    });
  }
  handlePickCard(args, user) {
    if (!this.me.isInDealt(args.card) || this.me.pick) return;
    this.me.pickCard(args.card);
    this.sensei.pickCard(args.card);
    this.me.revealCards();
    this.judgeRound(this.me);
  }
  updateProgress(user, won) {
    if (!user) return;
    if (this.checkBlackBeltWin(user, won)) {
      user.update({
        ninjaProgress: 100
      });
    }
    super.updateProgress(user, won);
  }
  checkBlackBeltWin(user, won) {
    return user.ninjaRank == 9 && won;
  }
  getNinja(seat) {
    return [this.sensei, this.me][seat];
  }
}
exports.default = SenseiInstance;