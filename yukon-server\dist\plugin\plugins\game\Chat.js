"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Chat extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'send_message': this.sendMessage,
      'send_safe': this.sendSafe,
      'send_emote': this.sendEmote,
      'send_joke': this.sendJoke,
      'send_tour': this.sendTour
    };
    this.commands = {
      'ai': this.addItem,
      'af': this.addFurniture,
      'ac': this.addCoins,
      'jr': this.joinRoom,
      'id': this.id,
      'users': this.userPopulation
    };
    this.bindCommands();
    this.messageRegex = /[^ -~]/i;
    this.maxMessageLength = 48;
  }

  // Events

  sendMessage(args, user) {
    if (!(0, _validation.hasProps)(args, 'message')) {
      return;
    }
    if (!(0, _validation.isString)(args.message)) {
      return;
    }
    if (this.messageRegex.test(args.message)) {
      return;
    }

    // Remove extra whitespace
    args.message = args.message.replace(/  +/g, ' ').trim();
    if (!(0, _validation.isLength)(args.message, 1, this.maxMessageLength)) {
      return;
    }
    if (args.message.startsWith('!') && this.processCommand(args.message, user)) {
      return;
    }
    user.room.send(user, 'send_message', {
      id: user.id,
      message: args.message
    }, [user], true);
  }
  sendSafe(args, user) {
    if (!(0, _validation.hasProps)(args, 'safe')) {
      return;
    }
    if (!(0, _validation.isNumber)(args.safe)) {
      return;
    }
    user.room.send(user, 'send_safe', {
      id: user.id,
      safe: args.safe
    }, [user], true);
  }
  sendEmote(args, user) {
    if (!(0, _validation.hasProps)(args, 'emote')) {
      return;
    }
    if (!(0, _validation.isNumber)(args.emote)) {
      return;
    }
    user.room.send(user, 'send_emote', {
      id: user.id,
      emote: args.emote
    }, [user], true);
  }
  sendJoke(args, user) {
    if (!(0, _validation.hasProps)(args, 'joke')) {
      return;
    }
    if (!(0, _validation.isNumber)(args.joke)) {
      return;
    }
    user.room.send(user, 'send_joke', {
      id: user.id,
      joke: args.joke
    }, [user], true);
  }
  sendTour(args, user) {
    if (!(0, _validation.hasProps)(args, 'roomId')) {
      return;
    }
    if (!(0, _validation.isNumber)(args.roomId)) {
      return;
    }
    if (args.roomId !== user.room.id) {
      return;
    }
    user.room.send(user, 'send_tour', {
      id: user.id,
      roomId: args.roomId
    }, [user], true);
  }

  // Commands

  bindCommands() {
    for (let command in this.commands) {
      this.commands[command] = this.commands[command].bind(this);
    }
  }
  processCommand(message, user) {
    message = message.substring(1);
    let args = message.split(' ');
    let command = args.shift().toLowerCase();
    if (command in this.commands) {
      this.commands[command](args, user);
      return true;
    }
    return false;
  }
  addItem(args, user) {
    if (user.isModerator) {
      this.plugins.item.addItem({
        item: args[0]
      }, user);
    }
  }
  addFurniture(args, user) {
    if (user.isModerator) {
      this.plugins.igloo.addFurniture({
        furniture: args[0]
      }, user);
    }
  }
  addCoins(args, user) {
    if (user.isModerator) {
      user.updateCoins(args[0], true);
    }
  }
  joinRoom(args, user) {
    if (!user.isModerator) {
      return;
    }
    let room = args[0];
    if (!room) {
      return;
    }
    if (!isNaN(room)) {
      this.plugins.join.joinRoom({
        room: parseInt(room)
      }, user);
      return;
    }
    room = Object.values(this.rooms).find(r => r.name == room.toLowerCase());
    if (room) {
      this.plugins.join.joinRoom({
        room: room.id
      }, user);
    }
  }
  id(args, user) {
    user.send('error', {
      error: `Your ID: ${user.id}`
    });
  }
  userPopulation(args, user) {
    user.send('error', {
      error: `Users online: ${this.handler.population}`
    });
  }
}
exports.default = Chat;