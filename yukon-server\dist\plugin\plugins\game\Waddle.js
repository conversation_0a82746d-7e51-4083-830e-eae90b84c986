"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Waddle extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'get_waddles': this.getWaddles,
      'join_waddle': this.joinWaddle,
      'leave_waddle': this.leaveWaddle
    };
  }
  getWaddles(args, user) {
    let waddles = Object.fromEntries(Object.values(user.room.waddles).map(waddle => {
      let users = waddle.users.map(user => user ? user.username : null);
      return [waddle.id, users];
    }));
    user.send('get_waddles', {
      waddles: waddles
    });
  }
  joinWaddle(args, user) {
    let waddle = user.room.waddles[args.waddle];
    if (!waddle) {
      return;
    }
    if (waddle.notFull && !user.waddle) {
      waddle.add(user);
    }
  }
  leaveWaddle(args, user) {
    if (user.waddle) {
      user.waddle.remove(user);
    }
  }
}
exports.default = Waddle;