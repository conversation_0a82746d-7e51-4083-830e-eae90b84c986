"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _Plugin = _interopRequireDefault(require("./Plugin"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class GamePlugin extends _Plugin.default {
  constructor(handler) {
    super(handler);
    this.usersById = handler.usersById;
    this.openIgloos = handler.openIgloos;
  }
  get crumbs() {
    return this.handler.crumbs;
  }
  get rooms() {
    return this.handler.rooms;
  }
}
exports.default = GamePlugin;