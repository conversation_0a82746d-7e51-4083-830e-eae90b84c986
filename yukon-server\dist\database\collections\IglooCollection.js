"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _Collection = _interopRequireDefault(require("../Collection"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class IglooCollection extends _Collection.default {
  constructor(user, models) {
    super(user, models, 'iglooInventories', 'iglooId');
  }
  add(igloo) {
    super.add({
      userId: this.user.id,
      iglooId: igloo
    });
  }
}
exports.default = IglooCollection;