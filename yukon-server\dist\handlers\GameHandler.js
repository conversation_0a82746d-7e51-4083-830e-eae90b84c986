"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseHandler = _interopRequireDefault(require("./BaseHandler"));
var _MatchmakerFactory = _interopRequireDefault(require("../objects/room/matchmaker/MatchmakerFactory"));
var _OpenIgloos = _interopRequireDefault(require("../objects/room/OpenIgloos"));
var _Room = _interopRequireDefault(require("../objects/room/Room"));
var _TableFactory = _interopRequireDefault(require("../objects/room/table/TableFactory"));
var _Waddle = _interopRequireDefault(require("../objects/room/waddle/Waddle"));
var _data = _interopRequireDefault(require("../../data/data"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class GameHandler extends _BaseHandler.default {
  constructor(id, users, db, config) {
    super(id, users, db, config);
    this.crumbs = {
      items: _data.default.items,
      igloos: _data.default.igloos,
      furnitures: _data.default.furnitures,
      floorings: _data.default.floorings,
      cards: _data.default.cards
    };
    this.usersById = {};
    this.maxUsers = config.worlds[id].maxUsers;
    this.rooms = this.setRooms();
    this.setTables();
    this.setWaddles();
    this.setMatchmakers();
    this.openIgloos = new _OpenIgloos.default();
    this.startPlugins('/game');
    this.updateWorldPopulation();
  }
  setRooms() {
    let rooms = {};
    for (let room of _data.default.rooms) {
      rooms[room.id] = new _Room.default(room);
    }
    return rooms;
  }
  setTables() {
    for (let table of _data.default.tables) {
      let room = this.rooms[table.roomId];
      room.tables[table.id] = _TableFactory.default.createTable(table, room);
    }
  }
  setWaddles() {
    for (let waddle of _data.default.waddles) {
      this.rooms[waddle.roomId].waddles[waddle.id] = new _Waddle.default(waddle);
    }
  }
  setMatchmakers() {
    for (let id in _data.default.matchmakers) {
      let room = this.rooms[id];
      room.matchmaker = _MatchmakerFactory.default.createMatchmaker(_data.default.matchmakers[id], room);
    }
  }
  handleGuard(message, user) {
    return !user.authenticated && message.action != 'game_auth';
  }
  close(user) {
    try {
      if (!user) {
        return;
      }
      if (!user.authenticated) {
        return this.closeAndUpdatePopulation(user);
      }
      if (user.room) {
        user.room.remove(user);
      }
      if (user.buddies) {
        user.buddies.sendOffline();
      }
      if (user.waddle) {
        user.waddle.remove(user);
      }
      if (user.minigameRoom) {
        user.minigameRoom.remove(user);
      }
      if (user.id && user.id in this.usersById) {
        delete this.usersById[user.id];
      }
      if (user.id) {
        this.openIgloos.remove(user);
      }
      if (user.pets) {
        user.pets.stopPetUpdate();
      }
      this.closeAndUpdatePopulation(user);
    } catch (error) {
      this.error(error);
    }
  }
  get joined() {
    return Object.values(this.users).filter(user => user.joinedServer);
  }
  get population() {
    return this.joined.length;
  }
  closeAndUpdatePopulation(user) {
    super.close(user);
    this.updateWorldPopulation();
  }
  updateWorldPopulation() {
    this.db.worlds.update({
      population: this.population
    }, {
      where: {
        id: this.id
      }
    });
  }
}
exports.default = GameHandler;