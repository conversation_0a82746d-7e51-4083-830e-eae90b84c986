const bcrypt = require('bcrypt');
const mysql = require('mysql2/promise');

async function createUser(username, password, email = '<EMAIL>') {
    try {
        // Connect to database
        const connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'yukon'
        });

        // Hash password using bcrypt (same as PHP)
        const hashedPassword = await bcrypt.hash(password, 10);
        // Convert $2b$ to $2a$ to match PHP bcrypt format
        const phpCompatibleHash = hashedPassword.replace('$2b$', '$2a$');

        // Check if user already exists
        const [existing] = await connection.execute(
            'SELECT username FROM users WHERE username = ?',
            [username]
        );

        if (existing.length > 0) {
            console.log(`❌ User '${username}' already exists!`);
            await connection.end();
            return false;
        }

        // Insert new user
        const [result] = await connection.execute(
            'INSERT INTO users (username, email, password) VALUES (?, ?, ?)',
            [username, email, phpCompatibleHash]
        );

        console.log(`✅ User '${username}' created successfully with ID: ${result.insertId}`);
        console.log(`   Email: ${email}`);
        console.log(`   Password: ${password}`);
        
        await connection.end();
        return true;

    } catch (error) {
        console.error('❌ Error creating user:', error.message);
        return false;
    }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 2) {
    console.log('Usage: node create-user.js <username> <password> [email]');
    console.log('Example: node create-user.js testuser password123');
    process.exit(1);
}

const username = args[0];
const password = args[1];
const email = args[2] || '<EMAIL>';

// Validate inputs
if (username.length < 4 || username.length > 12) {
    console.log('❌ Username must be between 4 and 12 characters');
    process.exit(1);
}

if (password.length < 3) {
    console.log('❌ Password must be at least 3 characters');
    process.exit(1);
}

// Create the user
createUser(username, password, email);
