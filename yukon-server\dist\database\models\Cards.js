"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseModel = _interopRequireDefault(require("../BaseModel"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Cards extends _BaseModel.default {
  static init(sequelize, DataTypes) {
    return super.init({
      userId: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true
      },
      cardId: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true
      },
      quantity: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      memberQuantity: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      }
    }, {
      sequelize,
      timestamps: false,
      tableName: 'cards'
    });
  }
  static associate({
    users
  }) {
    this.belongsTo(users, {
      foreignKey: 'userId'
    });
  }
}
exports.default = Cards;