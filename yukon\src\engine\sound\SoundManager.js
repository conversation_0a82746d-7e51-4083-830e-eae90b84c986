import { Howl, Howler } from 'howler'


export default class SoundManager {

    constructor(game) {
        this.cache = game.cache.audio

        // Active howl objects
        this.sounds = {}

        this.currentMusic
        this.muteMusic = false
        this.audioEnabled = false
        this.pendingMusic = null

        // Enable audio on first user interaction
        this.enableAudioOnInteraction()
    }

    enableAudioOnInteraction() {
        const enableAudio = () => {
            if (!this.audioEnabled) {
                console.log('Enabling audio after user interaction')
                this.audioEnabled = true

                // Try to unlock audio context
                if (Howler.ctx && Howler.ctx.state === 'suspended') {
                    Howler.ctx.resume().then(() => {
                        console.log('Audio context resumed')
                    }).catch(err => {
                        console.error('Failed to resume audio context:', err)
                    })
                }

                // Play pending music if any
                if (this.pendingMusic) {
                    console.log('Playing pending music:', this.pendingMusic)
                    this.playMusic(this.pendingMusic)
                    this.pendingMusic = null
                }

                // Remove event listeners after first interaction
                document.removeEventListener('click', enableAudio)
                document.removeEventListener('touchstart', enableAudio)
                document.removeEventListener('keydown', enableAudio)
            }
        }

        // Listen for user interactions
        document.addEventListener('click', enableAudio)
        document.addEventListener('touchstart', enableAudio)
        document.addEventListener('keydown', enableAudio)
    }

    play(key, config = {}) {
        console.log('SoundManager.play called with key:', key, 'config:', config)

        if (!this.cache.exists(key)) {
            console.log('Audio cache does not contain key:', key)
            return
        }

        let sound

        if (key in this.sounds) {
            console.log('Sound already exists, playing existing sound')
            sound = this.sounds[key]
            sound.play()

        } else {
            console.log('Creating new sound for key:', key)
            sound = this.add(key, config)
        }

        return sound
    }

    playMusic(key) {
        console.log('SoundManager.playMusic called with key:', key)
        console.log('muteMusic:', this.muteMusic)
        console.log('audioEnabled:', this.audioEnabled)
        console.log('currentMusic:', this.currentMusic)

        if (this.muteMusic) {
            console.log('Music is muted, returning early')
            return
        }

        if (!this.audioEnabled) {
            console.log('Audio not enabled yet (waiting for user interaction), storing as pending music')
            this.pendingMusic = key
            return
        }

        if (this.currentMusic && this.currentMusic.key == key) {
            console.log('Same music already playing, returning early')
            return
        }

        this.stopMusic()

        let music = this.play(key, { loop: true })
        console.log('Created music object:', music)

        if (music) {
            this.currentMusic = music
            console.log('Music set as current music')
        } else {
            console.log('Failed to create music object')
        }
    }

    stopAll() {
        this.stopAllButMusic()
        this.stopMusic()
    }

    stopAllButMusic() {
        for (let key in this.sounds) {
            let howl = this.sounds[key]
            if (howl != this.currentMusic) {
                this.remove(howl)
            }
        }
    }

    stopMusic() {
        if (this.currentMusic) {
            this.remove(this.currentMusic)
            this.currentMusic = null
        }
    }

    add(key, config) {
        console.log('SoundManager.add called with key:', key)

        config = {
            src: this.cache.get(key),
            format: 'mp3',
            ...config
        }

        console.log('Sound config:', config)

        let sound = new Howl(config)

        sound.key = key
        this.sounds[key] = sound

        sound.once('load', () => {
            console.log('Sound loaded successfully, playing:', key)
            sound.play()
        })

        sound.once('loaderror', (id, error) => {
            console.error('Sound load error for key:', key, 'error:', error)
        })

        return sound
    }

    remove(howl) {
        howl.unload()
        delete this.sounds[howl.key]
    }

}
