const mysql = require('mysql2/promise');

async function giveCoins(username, coins = 10000) {
    try {
        // Connect to database
        const connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'yukon'
        });

        // Check if user exists
        const [existing] = await connection.execute(
            'SELECT id, username, coins FROM users WHERE username = ?',
            [username]
        );

        if (existing.length === 0) {
            console.log(`❌ User '${username}' not found!`);
            await connection.end();
            return false;
        }

        const user = existing[0];
        const newCoins = user.coins + coins;
        
        // Update user coins
        const [result] = await connection.execute(
            'UPDATE users SET coins = ? WHERE username = ?',
            [newCoins, username]
        );

        console.log(`✅ User '${username}' coins updated successfully!`);
        console.log(`   Previous coins: ${user.coins}`);
        console.log(`   Added: ${coins}`);
        console.log(`   New total: ${newCoins}`);
        
        await connection.end();
        return true;

    } catch (error) {
        console.error('❌ Error updating coins:', error.message);
        return false;
    }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
    console.log('Usage: node give-coins.js <username> [coins]');
    console.log('Example: node give-coins.js testuser 5000');
    console.log('Default coins: 10000');
    process.exit(1);
}

const username = args[0];
const coins = parseInt(args[1]) || 10000;

// Give coins to user
giveCoins(username, coins);
