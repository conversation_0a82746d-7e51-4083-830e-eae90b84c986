"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _data = _interopRequireDefault(require("../../../../data/data"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Sensei extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'add_starter_deck': this.addStarterDeck
    };
    this.starterDeckId = 821;
    this.starterDeck = this.crumbs.items[this.starterDeckId];
  }
  addStarterDeck(args, user) {
    if (user.inventory.includes(this.starterDeckId)) {
      return;
    }
    const deck = _data.default.decks[this.starterDeckId];
    for (const card of deck) {
      if (_data.default.cards[card].powerId === 0) {
        user.cards.add(card);
      }
    }
    const powerCards = deck.filter(card => _data.default.cards[card].powerId > 0);
    const randomPowerCard = powerCards[Math.floor(Math.random() * powerCards.length)];
    user.cards.add(randomPowerCard);
    user.inventory.add(this.starterDeckId);
    user.send('add_item', {
      item: this.starterDeckId,
      name: this.starterDeck.name,
      slot: 'award',
      coins: user.coins
    });
  }
}
exports.default = Sensei;