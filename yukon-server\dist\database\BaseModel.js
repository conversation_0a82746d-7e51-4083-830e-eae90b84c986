"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _sequelize = _interopRequireDefault(require("sequelize"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class BaseModel extends _sequelize.default.Model {
  protectedAttributes = [];
  toJSON() {
    let attributes = this.get();
    for (let attribute of this.protectedAttributes) {
      delete attributes[attribute];
    }
    return attributes;
  }
}
exports.default = BaseModel;