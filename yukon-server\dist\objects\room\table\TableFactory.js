"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _FourTable = _interopRequireDefault(require("./FourTable"));
var _MancalaTable = _interopRequireDefault(require("./MancalaTable"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class TableFactory {
  static types = {
    'four': _FourTable.default,
    'mancala': _MancalaTable.default
  };
  static createTable(table, room) {
    return new this.types[table.game](table, room);
  }
}
exports.default = TableFactory;