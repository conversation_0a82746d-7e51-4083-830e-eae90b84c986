import Boot from '@engine/boot/Boot'
import Network from '@engine/network/Network'
import registerNinePatchContainerFactory from '@engine/utils/ninepatch/registerNinePatchContainerFactory'
import SoundManager from '@engine/sound/SoundManager'

import game from './data/game'
import './styles/game.css'


export default class Game extends Phaser.Game {

    constructor(config) {
        super(config)

        this.logBanner()

        // Removes focus from active element
        this.canvas.addEventListener('click', () => document.activeElement.blur())

        this.crumbs = config.crumbs
        this.network = new Network(this)

        // howler.js based sound manager
        this.soundManager = new SoundManager(this)

        registerNinePatchContainerFactory()

        this.scene.add('Boot', Boot, true)

        // Add global music test function for debugging
        window.testMusic = (musicId) => {
            console.log('Testing music:', musicId)
            this.soundManager.playMusic(musicId)
        }

        window.stopMusic = () => {
            console.log('Stopping music')
            this.soundManager.stopMusic()
        }

        // Add music status indicator
        this.createMusicStatusIndicator()
    }

    createMusicStatusIndicator() {
        // Create a visual indicator for music status
        const indicator = document.createElement('div')
        indicator.id = 'music-status'
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            max-width: 200px;
        `
        indicator.innerHTML = 'Music: Not loaded'
        document.body.appendChild(indicator)

        // Update indicator when music changes
        const updateIndicator = () => {
            const sm = this.soundManager
            let status = 'Music: '

            if (!sm.audioEnabled) {
                status += 'Waiting for user interaction'
            } else if (sm.currentMusic) {
                status += `Playing: ${sm.currentMusic.key}`
            } else if (sm.pendingMusic) {
                status += `Pending: ${sm.pendingMusic}`
            } else {
                status += 'Ready (no music)'
            }

            indicator.innerHTML = status
        }

        // Update every second
        setInterval(updateIndicator, 1000)
        updateIndicator()
    }

    logBanner() {
        // Please leave this line here for credit purposes
        console.log('%cYukon Client\nhttps://github.com/wizguin/yukon', 'font-size: 25px;')
        console.log(`Version ${VERSION}`)
    }

}

window.onload = () => {
    new Game(game)
}
