"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _SenseiInstance = _interopRequireDefault(require("../../../objects/instance/card/SenseiInstance"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Sensei extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'join_sensei': this.joinSensei
    };
    this.senseiRoom = 951;
  }
  joinSensei(args, user) {
    if (user.room.id != this.senseiRoom) {
      return;
    }
    if (!user.cards.hasCards) {
      return;
    }
    let instance = new _SenseiInstance.default(user);
    instance.init();
  }
}
exports.default = Sensei;