"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Table extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'get_tables': this.getTables,
      'join_table': this.joinTable,
      'leave_table': this.leaveTable
    };
  }
  getTables(args, user) {
    let tables = Object.fromEntries(Object.values(user.room.tables).map(table => {
      let users = table.users.map(user => user.username);
      return [table.id, users];
    }));
    user.send('get_tables', {
      tables: tables
    });
  }
  joinTable(args, user) {
    if (!(0, _validation.isNumber)(args.table)) {
      return;
    }
    let table = user.room.tables[args.table];
    user.joinTable(table);
  }
  leaveTable(args, user) {
    if (user.minigameRoom) {
      user.minigameRoom.remove(user);
    }
  }
}
exports.default = Table;