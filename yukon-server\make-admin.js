const mysql = require('mysql2/promise');

async function makeAdmin(username, rank = 2) {
    try {
        // Connect to database
        const connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'yukon'
        });

        // Check if user exists
        const [existing] = await connection.execute(
            'SELECT id, username, rank FROM users WHERE username = ?',
            [username]
        );

        if (existing.length === 0) {
            console.log(`❌ User '${username}' not found!`);
            await connection.end();
            return false;
        }

        const user = existing[0];
        
        // Update user rank
        const [result] = await connection.execute(
            'UPDATE users SET rank = ? WHERE username = ?',
            [rank, username]
        );

        console.log(`✅ User '${username}' updated successfully!`);
        console.log(`   Previous rank: ${user.rank}`);
        console.log(`   New rank: ${rank} ${rank >= 2 ? '(Moderator)' : '(Regular User)'}`);
        
        await connection.end();
        return true;

    } catch (error) {
        console.error('❌ Error updating user:', error.message);
        return false;
    }
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
    console.log('Usage: node make-admin.js <username> [rank]');
    console.log('Example: node make-admin.js admin 2');
    console.log('Ranks: 1 = Regular User, 2+ = Moderator');
    process.exit(1);
}

const username = args[0];
const rank = parseInt(args[1]) || 2;

// Make the user admin
makeAdmin(username, rank);
