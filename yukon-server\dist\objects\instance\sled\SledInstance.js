"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseInstance = _interopRequireDefault(require("../BaseInstance"));
var _validation = require("../../../utils/validation");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class SledInstance extends _BaseInstance.default {
  constructor(waddle) {
    super(waddle);
    this.id = 999;
    this.coins = [20, 10, 5, 5];
  }
  addListeners(user) {
    super.addListeners(user);
  }
  removeListeners(user) {
    super.removeListeners(user);
  }
  start() {
    const users = this.users.map(user => {
      return {
        username: user.username,
        color: user.color,
        hand: user.hand
      };
    });
    this.send('start_game', {
      users: users
    });
    super.start();
  }
  sendMove(args, user) {
    if (!(0, _validation.hasProps)(args, 'move')) {
      return;
    }
    if (!(0, _validation.isInRange)(args.move, 1, 5)) {
      return;
    }
    if (args.move === 5) {
      return this.sendGameOver(user);
    }
    this.send('send_move', {
      id: this.getSeat(user),
      move: args.move
    }, user);
  }
  sendGameOver(user) {
    this.remove(user);
    user.updateCoins(this.coins.shift(), true);
  }
}
exports.default = SledInstance;