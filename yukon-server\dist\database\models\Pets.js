"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseModel = _interopRequireDefault(require("../BaseModel"));
var _math = require("../../utils/math");
var _pick = _interopRequireDefault(require("../../utils/pick"));
var _sequelize = _interopRequireDefault(require("sequelize"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Pets extends _BaseModel.default {
  x = 0;
  y = 0;
  walking = false;
  get hungry() {
    return this.energy < 10;
  }
  get dead() {
    return this.energy === 0 || this.health === 0 || this.rest === 0;
  }
  get happiness() {
    const statTotal = this.energy + this.health + this.rest;
    return Math.round(statTotal / 300 * 100);
  }
  static init(sequelize, DataTypes) {
    return super.init({
      id: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true,
        autoIncrement: true
      },
      userId: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      typeId: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      name: {
        type: DataTypes.STRING(12),
        allowNull: false
      },
      adoptionDate: {
        type: _sequelize.default.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      energy: {
        type: DataTypes.INTEGER(3),
        allowNull: false,
        defaultValue: 100
      },
      health: {
        type: DataTypes.INTEGER(3),
        allowNull: false,
        defaultValue: 100
      },
      rest: {
        type: DataTypes.INTEGER(3),
        allowNull: false,
        defaultValue: 100
      },
      feedPostcardId: {
        type: DataTypes.INTEGER(11),
        allowNull: true,
        defaultValue: null
      }
    }, {
      sequelize,
      timestamps: false,
      tableName: 'pets'
    });
  }
  static associate({
    users
  }) {
    this.belongsTo(users, {
      foreignKey: 'userId'
    });
  }
  updateStats(updates) {
    // Apply current  stats
    for (const stat in updates) {
      updates[stat] = (0, _math.clamp)(this[stat] + updates[stat], 0, 100);
    }
    this.update(updates);
  }
  toJSON() {
    return (0, _pick.default)(this, 'id', 'typeId', 'name', 'energy', 'health', 'rest', 'x', 'y', 'walking');
  }
}
exports.default = Pets;