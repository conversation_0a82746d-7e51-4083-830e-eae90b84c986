"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _fs = _interopRequireDefault(require("fs"));
var _path = _interopRequireDefault(require("path"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class PluginManager {
  constructor(handler, pluginsDir) {
    this.events = handler.events;
    this.id = handler.id;
    this.dir = `${__dirname}/plugins${pluginsDir}`;
    this.plugins = {};
    this.loadPlugins(handler);
  }
  loadPlugins(handler) {
    let plugins = _fs.default.readdirSync(this.dir).filter(file => {
      return _path.default.extname(file) == '.js';
    });
    for (let plugin of plugins) {
      let pluginImport = require(_path.default.join(this.dir, plugin)).default;
      let pluginObject = new pluginImport(handler);
      this.plugins[plugin.replace('.js', '').toLowerCase()] = pluginObject;
      this.loadEvents(pluginObject);
    }
    let pluginsCount = Object.keys(this.plugins).length;
    let eventsCount = this.events._eventsCount;
    console.log(`[${this.id}] Loaded ${pluginsCount} plugins and ${eventsCount} events`);
  }
  loadEvents(plugin) {
    for (let event in plugin.events) {
      this.events.on(event, plugin.events[event].bind(plugin));
    }
  }
}
exports.default = PluginManager;