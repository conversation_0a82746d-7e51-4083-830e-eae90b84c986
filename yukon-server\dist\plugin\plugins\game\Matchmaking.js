"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _GamePlugin = _interopRequireDefault(require("../../GamePlugin"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Matchmaking extends _GamePlugin.default {
  constructor(handler) {
    super(handler);
    this.events = {
      'join_matchmaking': this.joinMatchmaking,
      'leave_matchmaking': this.leaveMatchmaking
    };
  }
  joinMatchmaking(args, user) {
    if (!user.room.matchmaker) {
      return;
    }
    if (!user.room.matchmaker.includes(user)) {
      user.room.matchmaker.add(user);
    }
  }
  leaveMatchmaking(args, user) {
    if (!user.room.matchmaker) {
      return;
    }
    if (user.room.matchmaker.includes(user)) {
      user.room.matchmaker.remove(user);
    }
  }
}
exports.default = Matchmaking;