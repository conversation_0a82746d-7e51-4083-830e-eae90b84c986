"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _getSocketAddress = _interopRequireDefault(require("./getSocketAddress"));
var _crypto = _interopRequireDefault(require("crypto"));
var _sequelize = require("sequelize");
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
var _default = exports.default = {
  init(server, socket) {
    this.server = server;
    this.socket = socket;
    this.db = server.db;
    this.handler = server.handler;
    this.config = server.config;
    this.address = (0, _getSocketAddress.default)(socket, this.config);
    this.loginSent = false;
    this.isModerator = false;
  },
  send(action, args = {}) {
    this.socket.emit('message', {
      action: action,
      args: args
    });
  },
  close() {
    this.socket.disconnect(true);
  },
  getId() {
    return this.id ? this.id : this.socket.id;
  },
  createLoginHash(randomKey) {
    let userAgent = this.socket.request.headers['user-agent'];
    let string = `${this.username}${randomKey}${this.address}${userAgent}`;
    return _crypto.default.createHash('sha256').update(string).digest('hex');
  },
  async load(username, selector = null) {
    return await this.reload({
      where: {
        username: username
      },
      include: [{
        model: this.db.authTokens,
        as: 'authToken',
        where: {
          selector: selector
        },
        required: false
      }, {
        model: this.db.bans,
        as: 'ban',
        where: {
          expires: {
            [_sequelize.Op.gt]: Date.now()
          }
        },
        required: false
      }]
    }).then(() => {
      this.setPermissions();
      return true;
    }).catch(error => {
      //this.handler.error(error)

      return false;
    });
  },
  setPermissions() {
    this.isModerator = this.rank >= 2;
  }
};