"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _BaseModel = _interopRequireDefault(require("../BaseModel"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class Igloos extends _BaseModel.default {
  static init(sequelize, DataTypes) {
    return super.init({
      userId: {
        type: DataTypes.INTEGER(11),
        allowNull: false,
        primaryKey: true
      },
      type: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      flooring: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      music: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      location: {
        type: DataTypes.INTEGER(11),
        allowNull: false
      },
      locked: {
        type: DataTypes.BOOLEAN,
        allowNull: false
      }
    }, {
      sequelize,
      timestamps: false,
      tableName: 'igloos'
    });
  }
  static associate({
    users
  }) {
    this.belongsTo(users, {
      foreignKey: 'userId'
    });
  }
}
exports.default = Igloos;