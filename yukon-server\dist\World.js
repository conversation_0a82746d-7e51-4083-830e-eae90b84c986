"use strict";

var _Database = _interopRequireDefault(require("./database/Database"));
var _GameHandler = _interopRequireDefault(require("./handlers/GameHandler"));
var _LoginHandler = _interopRequireDefault(require("./handlers/LoginHandler"));
var _Server = _interopRequireDefault(require("./server/Server"));
var _config = _interopRequireDefault(require("../config/config.json"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
class World extends _Server.default {
  constructor(id) {
    console.log(`[${id}] Starting world ${id} on port ${_config.default.worlds[id].port}`);
    let users = {};
    let db = new _Database.default(_config.default.database);
    let handler = id == 'Login' ? _LoginHandler.default : _GameHandler.default;
    handler = new handler(id, users, db, _config.default);
    super(id, users, db, handler, _config.default);
  }
}
let args = process.argv.slice(2);
for (let world of args) {
  if (world in _config.default.worlds) {
    new World(world);
  }
}