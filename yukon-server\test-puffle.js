const mysql = require('mysql2/promise');

async function adoptPuffle(username, puffleType = 0, puffleName = 'Fluffy') {
    try {
        // Connect to database
        const connection = await mysql.createConnection({
            host: 'localhost',
            user: 'root',
            password: '',
            database: 'yukon'
        });

        // Get user info
        const [users] = await connection.execute(
            'SELECT id, username, coins FROM users WHERE username = ?',
            [username]
        );

        if (users.length === 0) {
            console.log(`❌ User '${username}' not found!`);
            await connection.end();
            return false;
        }

        const user = users[0];
        const puffleCost = 800; // All puffles cost 800 coins

        if (user.coins < puffleCost) {
            console.log(`❌ User '${username}' doesn't have enough coins! Has: ${user.coins}, Needs: ${puffleCost}`);
            await connection.end();
            return false;
        }

        // Create the puffle
        const [petResult] = await connection.execute(
            'INSERT INTO pets (userId, typeId, name, energy, health, rest) VALUES (?, ?, ?, 100, 100, 100)',
            [user.id, puffleType, puffleName]
        );

        // Update user coins
        const newCoins = user.coins - puffleCost;
        await connection.execute(
            'UPDATE users SET coins = ? WHERE id = ?',
            [newCoins, user.id]
        );

        // Add adoption postcard
        await connection.execute(
            'INSERT INTO postcards (userId, postcardId, details) VALUES (?, 111, ?)',
            [user.id, puffleName]
        );

        console.log(`✅ Puffle adopted successfully!`);
        console.log(`   User: ${username}`);
        console.log(`   Puffle Type: ${puffleType} (${getPuffleColor(puffleType)})`);
        console.log(`   Puffle Name: ${puffleName}`);
        console.log(`   Pet ID: ${petResult.insertId}`);
        console.log(`   Coins before: ${user.coins}`);
        console.log(`   Coins after: ${newCoins}`);
        
        await connection.end();
        return true;

    } catch (error) {
        console.error('❌ Error adopting puffle:', error.message);
        return false;
    }
}

function getPuffleColor(typeId) {
    const colors = {
        0: 'Blue',
        1: 'Pink', 
        2: 'Black',
        3: 'Green',
        4: 'Purple',
        5: 'Red',
        6: 'Yellow',
        7: 'White',
        8: 'Orange',
        9: 'Brown'
    };
    return colors[typeId] || 'Unknown';
}

// Get command line arguments
const args = process.argv.slice(2);

if (args.length < 1) {
    console.log('Usage: node test-puffle.js <username> [puffleType] [puffleName]');
    console.log('Example: node test-puffle.js testuser 0 Fluffy');
    console.log('Puffle Types: 0=Blue, 1=Pink, 2=Black, 3=Green, 4=Purple, 5=Red, 6=Yellow, 7=White, 8=Orange, 9=Brown');
    process.exit(1);
}

const username = args[0];
const puffleType = parseInt(args[1]) || 0;
const puffleName = args[2] || 'Fluffy';

// Adopt the puffle
adoptPuffle(username, puffleType, puffleName);
